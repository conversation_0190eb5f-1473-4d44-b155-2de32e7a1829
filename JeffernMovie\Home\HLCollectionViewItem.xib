<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.Cocoa.XIB" version="3.0" toolsVersion="21507" targetRuntime="MacOSX.Cocoa" propertyAccessControl="none" useAutolayout="YES" customObjectInstantitationMethod="direct">
    <dependencies>
        <deployment identifier="macosx"/>
        <plugIn identifier="com.apple.InterfaceBuilder.CocoaPlugin" version="21507"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <customObject id="-2" userLabel="File's Owner" customClass="HLCollectionViewItem">
            <connections>
                <outlet property="textLabel" destination="1Sd-c1-lID" id="roY-Si-bW1"/>
                <outlet property="view" destination="Hz6-mo-xeY" id="0bl-1N-x8E"/>
            </connections>
        </customObject>
        <customObject id="-1" userLabel="First Responder" customClass="FirstResponder"/>
        <customObject id="-3" userLabel="Application" customClass="NSObject"/>
        <customView id="Hz6-mo-xeY">
            <rect key="frame" x="0.0" y="0.0" width="563" height="60"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
            <subviews>
                <textField horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="1Sd-c1-lID">
                    <rect key="frame" x="5" y="5" width="553" height="50"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="50" id="5Ov-iw-veP"/>
                    </constraints>
                    <textFieldCell key="cell" lineBreakMode="clipping" borderStyle="bezel" alignment="center" bezelStyle="round" id="krE-1j-h8d">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    </textFieldCell>
                </textField>
            </subviews>
            <constraints>
                <constraint firstAttribute="trailing" secondItem="1Sd-c1-lID" secondAttribute="trailing" constant="5" id="AWF-N5-Q8u"/>
                <constraint firstItem="1Sd-c1-lID" firstAttribute="top" secondItem="Hz6-mo-xeY" secondAttribute="top" constant="5" id="Gzn-m7-lni"/>
                <constraint firstItem="1Sd-c1-lID" firstAttribute="leading" secondItem="Hz6-mo-xeY" secondAttribute="leading" constant="5" id="q1z-nc-GC3"/>
            </constraints>
            <point key="canvasLocation" x="180.5" y="233"/>
        </customView>
    </objects>
</document>
