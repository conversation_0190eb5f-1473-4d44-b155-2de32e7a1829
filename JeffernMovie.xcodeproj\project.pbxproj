// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 48;
	objects = {

/* Begin PBXBuildFile section */
		3E8A3AA42E2BE856000E0CED /* img in Resources */ = {isa = PBXBuildFile; fileRef = 3E8A3AA32E2BE856000E0CED /* img */; };
		4F03FC9123C05BFE00292A72 /* NSString+HLAddition.m in Sources */ = {isa = PBXBuildFile; fileRef = 4F03FC9023C05BFE00292A72 /* NSString+HLAddition.m */; };
		4FA3209B248B579C00DEA984 /* HybridNSURLProtocol.m in Sources */ = {isa = PBXBuildFile; fileRef = 4FA3209A248B579C00DEA984 /* HybridNSURLProtocol.m */; };
		4FA3209E248B5D1800DEA984 /* NSURLProtocol+WKWebVIew.m in Sources */ = {isa = PBXBuildFile; fileRef = 4FA3209D248B5D1800DEA984 /* NSURLProtocol+WKWebVIew.m */; };
		4FC0FB0623C824F200ADD778 /* HLCollectionViewItem.m in Sources */ = {isa = PBXBuildFile; fileRef = 4FC0FB0423C824F200ADD778 /* HLCollectionViewItem.m */; };
		4FC0FB0723C824F200ADD778 /* HLCollectionViewItem.xib in Resources */ = {isa = PBXBuildFile; fileRef = 4FC0FB0523C824F200ADD778 /* HLCollectionViewItem.xib */; };
		C7240AB11FA17AF7000684F1 /* JSONKit.m in Sources */ = {isa = PBXBuildFile; fileRef = C7240AAF1FA17AF7000684F1 /* JSONKit.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		C7240AB21FA17AF7000684F2 /* HLWebsiteMonitor.m in Sources */ = {isa = PBXBuildFile; fileRef = C7240AB31FA17AF7000684F3 /* HLWebsiteMonitor.m */; };
		C72BBE041F9DB2EC00C5141E /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = C72BBE031F9DB2EC00C5141E /* <EMAIL> */; };
		C72BBE231F9DE8B000C5141E /* NSView+ZCAddition.m in Sources */ = {isa = PBXBuildFile; fileRef = C72BBE111F9DE8B000C5141E /* NSView+ZCAddition.m */; };
		C72BBE271F9DE8B000C5141E /* HLHomeViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = C72BBE1B1F9DE8B000C5141E /* HLHomeViewController.m */; };
		C72BBE281F9DE8B000C5141E /* HLHomeWindowController.m in Sources */ = {isa = PBXBuildFile; fileRef = C72BBE1D1F9DE8B000C5141E /* HLHomeWindowController.m */; };
		C7D9E8401F9DA1BB00730A34 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = C7D9E83F1F9DA1BB00730A34 /* AppDelegate.m */; };
		C7D9E8451F9DA1BB00730A34 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = C7D9E8441F9DA1BB00730A34 /* Assets.xcassets */; };
		C7D9E8481F9DA1BB00730A34 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = C7D9E8461F9DA1BB00730A34 /* Main.storyboard */; };
		C7D9E84B1F9DA1BB00730A34 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = C7D9E84A1F9DA1BB00730A34 /* main.m */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		3E8A3AA32E2BE856000E0CED /* img */ = {isa = PBXFileReference; lastKnownFileType = folder; path = img; sourceTree = "<group>"; };
		4F03FC8F23C05BFE00292A72 /* NSString+HLAddition.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSString+HLAddition.h"; sourceTree = "<group>"; };
		4F03FC9023C05BFE00292A72 /* NSString+HLAddition.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSString+HLAddition.m"; sourceTree = "<group>"; };
		4FA32099248B579C00DEA984 /* HybridNSURLProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HybridNSURLProtocol.h; sourceTree = "<group>"; };
		4FA3209A248B579C00DEA984 /* HybridNSURLProtocol.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HybridNSURLProtocol.m; sourceTree = "<group>"; };
		4FA3209C248B5D1800DEA984 /* NSURLProtocol+WKWebVIew.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSURLProtocol+WKWebVIew.h"; sourceTree = "<group>"; };
		4FA3209D248B5D1800DEA984 /* NSURLProtocol+WKWebVIew.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSURLProtocol+WKWebVIew.m"; sourceTree = "<group>"; };
		4FC0FB0323C824F200ADD778 /* HLCollectionViewItem.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HLCollectionViewItem.h; sourceTree = "<group>"; };
		4FC0FB0423C824F200ADD778 /* HLCollectionViewItem.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HLCollectionViewItem.m; sourceTree = "<group>"; };
		4FC0FB0523C824F200ADD778 /* HLCollectionViewItem.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = HLCollectionViewItem.xib; sourceTree = "<group>"; };
		C7240AAF1FA17AF7000684F1 /* JSONKit.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JSONKit.m; sourceTree = "<group>"; };
		C7240AB01FA17AF7000684F1 /* JSONKit.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JSONKit.h; sourceTree = "<group>"; };
		C7240AB31FA17AF7000684F3 /* HLWebsiteMonitor.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HLWebsiteMonitor.m; sourceTree = "<group>"; };
		C7240AB41FA17AF7000684F4 /* HLWebsiteMonitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HLWebsiteMonitor.h; sourceTree = "<group>"; };
		C72BBE031F9DB2EC00C5141E /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		C72BBE101F9DE8B000C5141E /* NSView+ZCAddition.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSView+ZCAddition.h"; sourceTree = "<group>"; };
		C72BBE111F9DE8B000C5141E /* NSView+ZCAddition.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSView+ZCAddition.m"; sourceTree = "<group>"; };
		C72BBE1A1F9DE8B000C5141E /* HLHomeViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HLHomeViewController.h; sourceTree = "<group>"; };
		C72BBE1B1F9DE8B000C5141E /* HLHomeViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HLHomeViewController.m; sourceTree = "<group>"; };
		C72BBE1C1F9DE8B000C5141E /* HLHomeWindowController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HLHomeWindowController.h; sourceTree = "<group>"; };
		C72BBE1D1F9DE8B000C5141E /* HLHomeWindowController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HLHomeWindowController.m; sourceTree = "<group>"; };
		C7D9E83B1F9DA1BB00730A34 /* JEFFERNMOVIE.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = JEFFERNMOVIE.app; sourceTree = BUILT_PRODUCTS_DIR; };
		C7D9E83E1F9DA1BB00730A34 /* AppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		C7D9E83F1F9DA1BB00730A34 /* AppDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		C7D9E8441F9DA1BB00730A34 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		C7D9E8471F9DA1BB00730A34 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		C7D9E8491F9DA1BB00730A34 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		C7D9E84A1F9DA1BB00730A34 /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		C7D9E8381F9DA1BB00730A34 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		C72BBE0F1F9DE8B000C5141E /* Addition */ = {
			isa = PBXGroup;
			children = (
				C72BBE101F9DE8B000C5141E /* NSView+ZCAddition.h */,
				C72BBE111F9DE8B000C5141E /* NSView+ZCAddition.m */,
				4F03FC8F23C05BFE00292A72 /* NSString+HLAddition.h */,
				4F03FC9023C05BFE00292A72 /* NSString+HLAddition.m */,
				4FA32099248B579C00DEA984 /* HybridNSURLProtocol.h */,
				4FA3209A248B579C00DEA984 /* HybridNSURLProtocol.m */,
				4FA3209C248B5D1800DEA984 /* NSURLProtocol+WKWebVIew.h */,
				4FA3209D248B5D1800DEA984 /* NSURLProtocol+WKWebVIew.m */,
			);
			path = Addition;
			sourceTree = "<group>";
		};
		C72BBE121F9DE8B000C5141E /* Helper */ = {
			isa = PBXGroup;
			children = (
				C7240AB01FA17AF7000684F1 /* JSONKit.h */,
				C7240AAF1FA17AF7000684F1 /* JSONKit.m */,
				C7240AB41FA17AF7000684F4 /* HLWebsiteMonitor.h */,
				C7240AB31FA17AF7000684F3 /* HLWebsiteMonitor.m */,
			);
			path = Helper;
			sourceTree = "<group>";
		};
		C72BBE191F9DE8B000C5141E /* Home */ = {
			isa = PBXGroup;
			children = (
				C72BBE1A1F9DE8B000C5141E /* HLHomeViewController.h */,
				C72BBE1B1F9DE8B000C5141E /* HLHomeViewController.m */,
				C72BBE1C1F9DE8B000C5141E /* HLHomeWindowController.h */,
				C72BBE1D1F9DE8B000C5141E /* HLHomeWindowController.m */,
				4FC0FB0323C824F200ADD778 /* HLCollectionViewItem.h */,
				4FC0FB0423C824F200ADD778 /* HLCollectionViewItem.m */,
				4FC0FB0523C824F200ADD778 /* HLCollectionViewItem.xib */,
			);
			path = Home;
			sourceTree = "<group>";
		};
		C7D9E8321F9DA1BB00730A34 = {
			isa = PBXGroup;
			children = (
				C7D9E83D1F9DA1BB00730A34 /* JeffernMovie */,
				C7D9E83C1F9DA1BB00730A34 /* Products */,
			);
			sourceTree = "<group>";
		};
		C7D9E83C1F9DA1BB00730A34 /* Products */ = {
			isa = PBXGroup;
			children = (
				C7D9E83B1F9DA1BB00730A34 /* JEFFERNMOVIE.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		C7D9E83D1F9DA1BB00730A34 /* JeffernMovie */ = {
			isa = PBXGroup;
			children = (
				C7D9E83E1F9DA1BB00730A34 /* AppDelegate.h */,
				C7D9E83F1F9DA1BB00730A34 /* AppDelegate.m */,
				C72BBE0F1F9DE8B000C5141E /* Addition */,
				C72BBE121F9DE8B000C5141E /* Helper */,
				C72BBE191F9DE8B000C5141E /* Home */,
				C72BBE031F9DB2EC00C5141E /* <EMAIL> */,
				C7D9E8441F9DA1BB00730A34 /* Assets.xcassets */,
				C7D9E8461F9DA1BB00730A34 /* Main.storyboard */,
				C7D9E8491F9DA1BB00730A34 /* Info.plist */,
				C7D9E84A1F9DA1BB00730A34 /* main.m */,
				3E8A3AA32E2BE856000E0CED /* img */,
			);
			path = JeffernMovie;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		C7D9E83A1F9DA1BB00730A34 /* JeffernMovie */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C7D9E84F1F9DA1BB00730A34 /* Build configuration list for PBXNativeTarget "JeffernMovie" */;
			buildPhases = (
				C7D9E8371F9DA1BB00730A34 /* Sources */,
				C7D9E8381F9DA1BB00730A34 /* Frameworks */,
				C7D9E8391F9DA1BB00730A34 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = JeffernMovie;
			productName = JeffernMovie;
			productReference = C7D9E83B1F9DA1BB00730A34 /* JEFFERNMOVIE.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		C7D9E8331F9DA1BB00730A34 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				CLASSPREFIX = HL;
				LastUpgradeCheck = 0930;
				ORGANIZATIONNAME = SV;
				TargetAttributes = {
					C7D9E83A1F9DA1BB00730A34 = {
						CreatedOnToolsVersion = 9.0.1;
						ProvisioningStyle = Manual;
					};
				};
			};
			buildConfigurationList = C7D9E8361F9DA1BB00730A34 /* Build configuration list for PBXProject "JeffernMovie" */;
			compatibilityVersion = "Xcode 8.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = C7D9E8321F9DA1BB00730A34;
			productRefGroup = C7D9E83C1F9DA1BB00730A34 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				C7D9E83A1F9DA1BB00730A34 /* JeffernMovie */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		C7D9E8391F9DA1BB00730A34 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				3E8A3AA42E2BE856000E0CED /* img in Resources */,
				C7D9E8451F9DA1BB00730A34 /* Assets.xcassets in Resources */,
				C7D9E8481F9DA1BB00730A34 /* Main.storyboard in Resources */,
				4FC0FB0723C824F200ADD778 /* HLCollectionViewItem.xib in Resources */,
				C72BBE041F9DB2EC00C5141E /* <EMAIL> in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		C7D9E8371F9DA1BB00730A34 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C7D9E84B1F9DA1BB00730A34 /* main.m in Sources */,
				C7D9E8401F9DA1BB00730A34 /* AppDelegate.m in Sources */,
				C72BBE231F9DE8B000C5141E /* NSView+ZCAddition.m in Sources */,
				4F03FC9123C05BFE00292A72 /* NSString+HLAddition.m in Sources */,
				4FA3209E248B5D1800DEA984 /* NSURLProtocol+WKWebVIew.m in Sources */,
				4FA3209B248B579C00DEA984 /* HybridNSURLProtocol.m in Sources */,
				C7240AB11FA17AF7000684F1 /* JSONKit.m in Sources */,
				C7240AB21FA17AF7000684F2 /* HLWebsiteMonitor.m in Sources */,
				C72BBE281F9DE8B000C5141E /* HLHomeWindowController.m in Sources */,
				4FC0FB0623C824F200ADD778 /* HLCollectionViewItem.m in Sources */,
				C72BBE271F9DE8B000C5141E /* HLHomeViewController.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		C7D9E8461F9DA1BB00730A34 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				C7D9E8471F9DA1BB00730A34 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		C7D9E84D1F9DA1BB00730A34 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "-";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.11;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
			};
			name = Debug;
		};
		C7D9E84E1F9DA1BB00730A34 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "-";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.11;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = macosx;
			};
			name = Release;
		};
		C7D9E8501F9DA1BB00730A34 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				CODE_SIGN_ENTITLEMENTS = "";
				CODE_SIGN_IDENTITY = "-";
				CODE_SIGN_STYLE = Manual;
				COMBINE_HIDPI_IMAGES = YES;
				DEVELOPMENT_TEAM = "";
				INFOPLIST_FILE = JeffernMovie/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MARKETING_VERSION = 1.4.2;
				PRODUCT_BUNDLE_IDENTIFIER = com.lhl.macvipvideo;
				PRODUCT_NAME = JEFFERNMOVIE;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		C7D9E8511F9DA1BB00730A34 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				CODE_SIGN_ENTITLEMENTS = "";
				CODE_SIGN_IDENTITY = "-";
				CODE_SIGN_STYLE = Manual;
				COMBINE_HIDPI_IMAGES = YES;
				DEVELOPMENT_TEAM = "";
				INFOPLIST_FILE = JeffernMovie/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MARKETING_VERSION = 1.4.2;
				PRODUCT_BUNDLE_IDENTIFIER = com.lhl.macvipvideo;
				PRODUCT_NAME = JEFFERNMOVIE;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		C7D9E8361F9DA1BB00730A34 /* Build configuration list for PBXProject "JeffernMovie" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C7D9E84D1F9DA1BB00730A34 /* Debug */,
				C7D9E84E1F9DA1BB00730A34 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C7D9E84F1F9DA1BB00730A34 /* Build configuration list for PBXNativeTarget "JeffernMovie" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C7D9E8501F9DA1BB00730A34 /* Debug */,
				C7D9E8511F9DA1BB00730A34 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = C7D9E8331F9DA1BB00730A34 /* Project object */;
}
